-- =============================================
-- Enhanced Stored Procedures for Partial Payment Support
-- =============================================

USE [CabYaari]
GO

-- =============================================
-- Stored Procedure: usp_Booking_Create_V2
-- Description: Enhanced version with partial payment support
-- =============================================
CREATE PROCEDURE [dbo].[usp_Booking_Create_V2]   
    @Booking_Id nvarchar(30) NULL,  
    @PickUpCity nvarchar(30) NULL,  
    @DropOffCity nvarchar(30) NULL,  
    @TripType nvarchar(30) NULL,  
    @CarCategory nvarchar(30) NULL,  
    @Duration nvarchar(30) NULL,  
    @Distance decimal NULL,  
    @BasicFare decimal NULL,  
    @DriverCharge decimal,  
    @GST decimal NULL,  
    @Fare decimal NULL,  
    @GSTFare decimal NULL,  
    @CouponCode nvarchar(50) NULL,  
    @CouponDiscount decimal NULL,  
    @PickUpAddress nvarchar(200) NULL,  
    @DropOffAddress nvarchar(200) NULL,  
    @PickUpDate date NULL,  
    @PickUpTime nvarchar(10) NULL,  
    @TravelerName nvarchar(200) NULL,  
    @PhoneNumber nvarchar(30) NULL,  
    @MailId nvarchar(30) NULL,  
    @PaymentMode int NULL,  
    @BookingCreatedBy nvarchar(30) NULL,  
    @RazorpayPaymentID nvarchar(30) NULL,  
    @RazorpayOrderID nvarchar(30) NULL,  
    @RazorpaySignature nvarchar(30) NULL,  
    @RazorpayStatus nvarchar(30) NULL,  
    @PickUpAddressLongLat nvarchar(100) NULL,  
    @PickUpAddressLongitude nvarchar(100) NULL,  
    @CashAmountToPayDriver decimal,  
    @PaymentOption int NULL,  
    @TollCharge decimal NULL,
    -- New parameters for partial payment support
    @PaymentType nvarchar(10) NULL,
    @PartialPaymentAmount decimal(18,2) NULL,
    @RemainingAmountForDriver decimal(18,2) NULL,
    @result nvarchar(50) NULL output  
WITH RECOMPILE   
AS  
BEGIN  
    SET NOCOUNT ON;  
    SET XACT_ABORT ON;  
    
    DECLARE @errorMessage nvarchar(max),  
           @ScriptRanOn datetime = GETDATE(),  
           @Booking_Date datetime = GETDATE(),  
           @FromCityID int = 0,  
           @TripTypeID int = 0,  
           @CategoryID int = 0,  
           @ToCityID int = 0,
           @UserID int = 0
    
    -- Get city, trip type, category IDs
    SET @FromCityID = (SELECT TOP 1 PKID FROM RLT_CITY WHERE CitY_Name = @PickUpCity AND Is_Active = 1);
    SET @ToCityID = (SELECT TOP 1 PKID FROM RLT_CITY WHERE CitY_Name = @DropOffCity AND Is_Active = 1);
    SET @TripTypeID = (SELECT TOP 1 PKID FROM RLT_TRIP_TYPES WHERE Trip_Type = @TripType AND Is_Active = 1);
    SET @CategoryID = (SELECT TOP 1 PKID FROM RLT_CAR_CATEGORY WHERE Car_Category_Abbr = @CarCategory AND Is_Active = 1);

    -- Try to get user ID, but don't fail if not found
    SET @UserID = (SELECT TOP 1 ID FROM [Identity].[User] WHERE UserName = @BookingCreatedBy);
    IF @UserID IS NULL SET @UserID = 0;  -- Default to 0 if user not found
    
    -- Set default payment type if not provided
    IF @PaymentType IS NULL OR @PaymentType = ''
        SET @PaymentType = 'FULL'
    
    -- Validate partial payment logic
    IF @PaymentType = 'PARTIAL' AND (@PartialPaymentAmount IS NULL OR @PartialPaymentAmount <= 0)
    BEGIN
        RAISERROR('Partial payment amount must be greater than 0 for partial payments', 16, 1)
        RETURN
    END
    
    -- Calculate remaining amount for driver if not provided
    IF @PaymentType = 'PARTIAL' AND @RemainingAmountForDriver IS NULL
        SET @RemainingAmountForDriver = @CashAmountToPayDriver

    IF @PaymentType = 'FULL'
    BEGIN
        SET @PartialPaymentAmount = @Fare
        SET @RemainingAmountForDriver = 0
    END
    
    BEGIN TRANSACTION  
        INSERT INTO [dbo].[RLT_BOOKING](
            Booking_Id, City_From_Id, City_To_Id, Trip_Type_Id, Car_Category_Id,  
            Duration, Distance, Basic_Fare, Driver_Charge, GST, Fare, GST_Fare,  
            Coupon_Code, Coupon_Discount, Booking_Date, PickUp_Address, DropOff_Address,  
            PickUp_Date, PickUp_Time, [Name], Mobile_No1, Mail_Id, Mode_Of_Payment_Id,  
            Booking_Status_Id, Created_By, razorpay_payment_id, razorpay_order_id,  
            razorpay_signature, razorpay_status, PickUpAddressLatitude, PickUpAddressLongitude,  
            CashAmountToPayDriver, PaymentOption, TollCharge,
            -- New columns for partial payment
            PaymentType, PartialPaymentAmount, RemainingAmountForDriver
        ) VALUES (  
            @Booking_Id, @FromCityID, @ToCityID, @TripTypeID, @CategoryID,  
            @Duration, @Distance, @BasicFare, @DriverCharge, @GST, @Fare, @GSTFare,  
            @CouponCode, @CouponDiscount, GETDATE(), @PickUpAddress, @DropOffAddress,  
            @PickUpDate, @PickUpTime, @TravelerName, @PhoneNumber, @MailId, @PaymentMode,  
            1, /*New Booking Request*/ @UserID, @RazorpayPaymentID, @RazorpayOrderID,  
            @RazorpaySignature, @RazorpayStatus, @PickUpAddressLongLat, @PickUpAddressLongitude,  
            @CashAmountToPayDriver, @PaymentOption, @TollCharge,
            -- New values for partial payment
            @PaymentType, @PartialPaymentAmount, @RemainingAmountForDriver
        )  
           
        SET @errorMessage = CONVERT(nvarchar(max), GETDATE(), 21) + ' running usp_Booking_Create_V2 => final--commiting sql transaction'  
        RAISERROR(@errorMessage, 0, 1) WITH NOWAIT  
    COMMIT TRANSACTION   

    SET @result = SCOPE_IDENTITY()
END
GO

-- =============================================
-- Stored Procedure: usp_Booking_Update_V2
-- Description: Enhanced version with partial payment support
-- =============================================
CREATE PROCEDURE [dbo].[usp_Booking_Update_V2]
    @BookingId nvarchar(30),
    @RazorpayPaymentId nvarchar(30),
    @RazorpayOrderid nvarchar(30),
    @RazorpaySignature nvarchar(30),
    @RazorpayStatus nvarchar(30),
    -- New parameters for partial payment support
    @PaymentType nvarchar(10) NULL,
    @PartialPaymentAmount decimal(18,2) NULL,
    @RemainingAmountForDriver decimal(18,2) NULL
WITH RECOMPILE
AS
BEGIN
    SET NOCOUNT ON;
    SET XACT_ABORT ON;

    DECLARE @errorMessage nvarchar(max),
           @ScriptRanOn datetime = GETDATE(),
           @TransactionId nvarchar(30),
           @BookingStatusId int,
           @BookingPKID int,
           @CurrentPaymentType nvarchar(10),
           @CurrentPartialAmount decimal(18,2),
           @CurrentRemainingAmount decimal(18,2)

    -- Get current payment information
    SELECT
        @CurrentPaymentType = PaymentType,
        @CurrentPartialAmount = PartialPaymentAmount,
        @CurrentRemainingAmount = RemainingAmountForDriver
    FROM RLT_BOOKING
    WHERE Booking_Id = @BookingId

    -- Get the appropriate booking status ID based on payment status
    SET @BookingStatusId = CASE
        WHEN @RazorpayStatus IN ('Paid', 'COMPLETED') THEN 2  -- Confirmed/Paid booking
        WHEN @RazorpayStatus IN ('Failed') THEN 6  -- Cancelled booking (from BookingStatus enum)
        ELSE 1  -- Pending booking
    END

    BEGIN TRANSACTION
        -- Update the booking record with payment information
        UPDATE RLT_BOOKING
        SET
            razorpay_payment_id = @RazorpayPaymentId,
            razorpay_signature = @RazorpaySignature,
            razorpay_status = @RazorpayStatus,
            Booking_Status_Id = @BookingStatusId,
            -- Update payment fields only if new values are provided
            PaymentType = ISNULL(@PaymentType, @CurrentPaymentType),
            PartialPaymentAmount = ISNULL(@PartialPaymentAmount, @CurrentPartialAmount),
            RemainingAmountForDriver = ISNULL(@RemainingAmountForDriver, @CurrentRemainingAmount),
            Updated_Date = GETDATE()
        WHERE
            Booking_Id = @BookingId

        -- Get the updated booking information for the response
        SELECT
            @TransactionId = razorpay_payment_id,
            @BookingPKID = PKID,
            @PaymentType = ISNULL(@PaymentType, PaymentType),
            @PartialPaymentAmount = ISNULL(@PartialPaymentAmount, PartialPaymentAmount),
            @RemainingAmountForDriver = ISNULL(@RemainingAmountForDriver, RemainingAmountForDriver)
        FROM
            RLT_BOOKING
        WHERE
            Booking_Id = @BookingId

        SET @errorMessage = CONVERT(nvarchar(max), GETDATE(), 21) + ' running usp_Booking_Update_V2 => committing sql transaction'
        RAISERROR(@errorMessage, 0, 1) WITH NOWAIT
    COMMIT TRANSACTION

    -- Return the booking and transaction information including payment details
    SELECT
        @BookingId AS BookingId,
        @TransactionId AS TransactionId,
        @RazorpayStatus AS PaymentStatus,
        @PaymentType AS PaymentType,
        @PartialPaymentAmount AS PartialPaymentAmount,
        @RemainingAmountForDriver AS RemainingAmountForDriver,
        @BookingStatusId AS BookingStatusId
END
GO

-- =============================================
-- Stored Procedure: usp_BookingDetails_Get
-- Description: Retrieves booking details by booking ID with proper field mapping
-- =============================================
CREATE PROCEDURE [dbo].[usp_BookingDetails_Get]
    @bookingID nvarchar(30)
WITH RECOMPILE
AS
BEGIN
    SET NOCOUNT ON;

    SELECT
        Booking_Id AS BookingID,
        PickUp_City AS PickUpCity,
        DropOff_City AS DropOffCity,
        Trip_Type AS TripType,
        Car_Category AS CarCategory,
        Duration,
        Distance,
        Basic_Fare AS BasicFare,
        Driver_Charge AS DriverCharge,
        GST AS Gst,
        Fare,
        GST_Fare AS GstFare,
        Coupon_Code AS CouponCode,
        Coupon_Discount AS CouponDiscount,
        PickUp_Address AS PickUpAddress,
        DropOff_Address AS DropOffAddress,
        PickUp_Date AS PickUpDate,
        PickUp_Time AS PickUpTime,
        [Name] AS TravelerName,
        Mobile_No1 AS PhoneNumber,
        Mail_Id AS MailId,
        Mode_Of_Payment_Id AS PaymentMode,
        Booking_Created_By AS BookingCreatedBy,
        razorpay_payment_id AS RazorpayPaymentId,
        razorpay_order_id AS RazorpayOrderid,
        razorpay_signature AS RazorpaySignature,
        razorpay_status AS RazorpayStatus,
        PickUpAddressLatitude AS PickUpAddressLongLat,
        PickUpAddressLongitude AS DropOffAddressLongLat,
        CashAmountToPayDriver,
        PaymentOption,
        TollCharge,
        PaymentType,
        PartialPaymentAmount,
        RemainingAmountForDriver
    FROM RLT_BOOKING
    WHERE Booking_Id = @bookingID
END
GO

PRINT 'Enhanced stored procedures created successfully!'
PRINT 'usp_Booking_Create_V2: Supports partial payment creation'
PRINT 'usp_Booking_Update_V2: Supports partial payment updates'
PRINT 'usp_BookingDetails_Get: Retrieves booking details with proper field mapping'
